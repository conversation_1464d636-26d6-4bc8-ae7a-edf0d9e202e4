"use client";

import React from "react";
import { But<PERSON> } from "../ui/button";
import * as P from "@/components/ui/popover";
import { FriendData } from "./friends-client";
import Avatar from "../layout/avatar";
import { MessageCircle, MessageCircleOff, Trash } from "lucide-react";
import Chat from "./chat";
import { User } from "better-auth";

export default ({
  user,
  friends,
  onDenyRequest,
  loading,
}: {
  user: User;
  friends: FriendData[];
  onDenyRequest: (senderId: string, receiverId: string) => Promise<boolean>;
  loading: boolean;
}) => {
  const [chatOpen, setChatOpen] = React.useState<boolean>(false);

  if (loading) return <div className="text-center text-muted-foreground">Loading friends...</div>;
  if (friends.length === 0)
    return <div className="text-center text-muted-foreground">No friends yet. Add some friends to get started!</div>;
  return (
    <ul className="flex flex-col gap-2 w-full">
      {friends.map((f) => (
        <P.Popover key={f.id}>
          <P.PopoverTrigger asChild>
            <Button variant="default" className="flex items-center justify-start gap-2 rounded-lg cursor-pointer py-8">
              <Avatar img={f.image} />
              <span className="text-lg ml-2 truncate font-medium">{f.name}</span>
            </Button>
          </P.PopoverTrigger>
          <P.PopoverContent align="center" sideOffset={16} side="top" className="w-full flex flex-col justify-start items-center gap-2">
            <div className="flex flex-row justify-between items-center gap-2 flex-wrap w-full">
              <Avatar img={f.image} size="md" />
              <div className="grow">
                <div className="text-shadow-2xs w-full flex flex-col xs:flex-row xs:flex-wrap gap-1 xs:gap-2 items-start xs:items-center mb-2">
                  <span className="select-none text-sm xs:text-base font-medium min-w-[50px] xs:min-w-[60px]">Name:</span>
                  <span className={`truncate text-xs xs:text-sm select-all break-words w-full xs:w-auto`}>{f.name}</span>
                </div>

                <div className="text-shadow-2xs w-full flex flex-col xs:flex-row gap-1 xs:gap-2 items-start xs:items-center mb-2">
                  <span className="select-none text-sm xs:text-base font-medium min-w-[50px] xs:min-w-[60px]">Email:</span>
                  <span className={"truncate text-xs xs:text-sm select-all w-full xs:w-auto"}>{f.email}</span>
                </div>

                <div className="text-shadow-2xs w-full flex flex-col xs:flex-row gap-1 xs:gap-2 items-start xs:items-start">
                  <span className="select-none text-sm xs:text-base font-medium min-w-[50px] xs:min-w-[60px] mt-0.5">Bio:</span>
                  <span
                    className={`truncate text-xs xs:text-sm select-all break-words w-full xs:w-auto ${f.bio ? "text-inherit" : "text-muted-foreground"}`}>
                    {f.bio || "User has no bio"}
                  </span>
                </div>
              </div>
            </div>
            <div className="col-span-2 flex flex-row flex-wrap justify-start gap-2 w-full">
              <Button size="icon"></Button>
              <Button size="icon" onClick={() => setChatOpen(!chatOpen)}>
                {chatOpen ? <MessageCircleOff /> : <MessageCircle />}
              </Button>
              <Button
                size="icon"
                variant={"destructive"}
                onClick={async () => {
                  await onDenyRequest(f.sender, f.receiver);
                }}>
                <Trash />
              </Button>
            </div>
            <Chat me={user} you={f} chatOpen={chatOpen} />
          </P.PopoverContent>
        </P.Popover>
      ))}
    </ul>
  );
};
