"use client";

import { User } from "better-auth";
import React from "react";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";

export default ({
  me,
  you,
  chatOpen,
}: {
  me: Partial<User>;
  you: Omit<User, "image" | "email" | "emailVerified" | "createdAt" | "updatedAt" | "name">;
  chatOpen: boolean;
}) => {
  const [message, setMessage] = React.useState<string>("");
  if (!chatOpen || !me || !you) return null;
  return (
    <>
      <Textarea disabled />
      <div className="w-full flex flex-row gap-2">
        <Input type="text" value={message} onChange={(e) => setMessage(e.target.value)} />
        <Button>Send</Button>
      </div>
    </>
  );
};
