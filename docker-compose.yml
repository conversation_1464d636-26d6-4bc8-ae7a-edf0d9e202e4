services:
  app:
    image: node:20-alpine
    container_name: mystique_app
    working_dir: /app
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/node_modules
    command: npm ci && npm run build && npm run start;"
    depends_on:
      - postgres
      - redis
      - minio
    profiles:
      - production

  postgres:
    image: postgres:latest
    container_name: mystique_postgres
    restart: unless-stopped
    ports:
      - "${DATABASE_PORT}:5432"
    environment:
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    container_name: mystique_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio
    container_name: mystique_minio
    restart: unless-stopped
    ports:
      - "${S3_PORT}:9000"
      - "${S3_CONSOLE_PORT}:9001"
    environment:
      MINIO_ROOT_USER: ${S3_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${S3_ROOT_PASSWORD}
    command: server /data --console-address ":${S3_CONSOLE_PORT}"
    volumes:
      - minio_data:/data

volumes:
  postgres_data:
  redis_data:
  minio_data:
